#!/usr/bin/env python3
"""
OKX下单附带止盈止损演示脚本

演示如何在OKX下单时直接附带止盈止损参数
"""

import os
import ccxt
from dotenv import load_dotenv

def demo_okx_order_with_tpsl():
    """演示OKX下单附带止盈止损"""
    
    print("🚀 OKX下单附带止盈止损演示")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 错误: 请确保在.env文件中设置了OKX API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': True,  # 使用测试环境
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
            }
        })
        
        print("✅ OKX交易所连接成功（测试环境）")
        
        # 交易参数
        symbol = 'BTC/USDT:USDT'
        side = 'buy'
        amount = 0.001
        order_type = 'market'
        
        # 获取当前价格
        ticker = exchange.fetch_ticker(symbol)
        current_price = ticker['last']
        
        # 计算止盈止损价格
        tp_price = current_price * 1.02  # 2%止盈
        sl_price = current_price * 0.98  # 2%止损
        
        print(f"📊 当前价格: ${current_price:.2f}")
        print(f"🎯 止盈价格: ${tp_price:.2f}")
        print(f"🛡️ 止损价格: ${sl_price:.2f}")
        
        # 构造订单参数（附带止盈止损）
        order_params = {
            'marginMode': 'isolated',
            'posSide': 'long',
            'tpTriggerPx': str(tp_price),      # 止盈触发价
            'tpOrdPx': '-1',                   # 止盈委托价，-1表示市价
            'slTriggerPx': str(sl_price),      # 止损触发价  
            'slOrdPx': '-1',                   # 止损委托价，-1表示市价
            'tpTriggerPxType': 'last',         # 止盈触发价类型：last最新价
            'slTriggerPxType': 'last'          # 止损触发价类型：last最新价
        }
        
        print("\n📋 订单参数:")
        for key, value in order_params.items():
            print(f"  {key}: {value}")
        
        # 模拟下单（不实际执行）
        print(f"\n🔄 模拟下单:")
        print(f"  交易对: {symbol}")
        print(f"  方向: {side.upper()}")
        print(f"  数量: {amount}")
        print(f"  类型: {order_type.upper()}")
        print(f"  附带止盈止损: 是")
        
        # 实际的下单代码（注释掉以避免真实交易）
        """
        order = exchange.create_order(
            symbol=symbol,
            type=order_type,
            side=side,
            amount=amount,
            price=None,  # 市价单不需要价格
            params=order_params
        )
        
        if order and 'id' in order:
            print(f"✅ 订单提交成功: {order.get('id')}")
            print(f"📊 订单详情: {order}")
        else:
            print("❌ 订单提交失败")
        """
        
        print("\n✅ 演示完成")
        print("💡 提示: 实际使用时请取消注释下单代码")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        return False

def show_okx_tpsl_best_practices():
    """显示OKX止盈止损最佳实践"""
    print("\n📚 OKX止盈止损最佳实践:")
    print("=" * 50)
    
    print("1. 参数说明:")
    print("   tpTriggerPx: 止盈触发价格")
    print("   tpOrdPx: 止盈委托价格 (-1=市价)")
    print("   slTriggerPx: 止损触发价格")
    print("   slOrdPx: 止损委托价格 (-1=市价)")
    
    print("\n2. 触发价类型:")
    print("   last: 最新成交价（推荐）")
    print("   index: 指数价格")
    print("   mark: 标记价格")
    
    print("\n3. 优势:")
    print("   ✅ 一次性设置，减少API调用")
    print("   ✅ 原子操作，避免部分失败")
    print("   ✅ 更快的执行速度")
    print("   ✅ 减少网络延迟影响")
    
    print("\n4. 注意事项:")
    print("   ⚠️ 确保价格精度符合要求")
    print("   ⚠️ 止盈止损价格要合理")
    print("   ⚠️ 测试环境先验证参数")
    print("   ⚠️ 监控订单执行状态")

def compare_traditional_vs_attached():
    """对比传统方式与附带方式的差异"""
    print("\n🔄 传统方式 vs 附带方式对比:")
    print("=" * 50)
    
    print("传统方式（分步执行）:")
    print("1. 提交开仓订单")
    print("2. 等待订单成交")
    print("3. 查询持仓信息")
    print("4. 提交止盈订单")
    print("5. 提交止损订单")
    print("❌ 多次API调用，可能部分失败")
    
    print("\n附带方式（一次完成）:")
    print("1. 提交开仓订单（附带止盈止损）")
    print("✅ 一次API调用，原子操作")
    print("✅ 更快的执行速度")
    print("✅ 更高的成功率")

if __name__ == "__main__":
    # 运行演示
    success = demo_okx_order_with_tpsl()
    
    # 显示最佳实践
    show_okx_tpsl_best_practices()
    
    # 显示对比
    compare_traditional_vs_attached()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 演示成功完成")
    else:
        print("❌ 演示失败")
    
    print("🎯 建议: 在主程序中使用附带方式下单，提高交易效率")
