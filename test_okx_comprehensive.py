#!/usr/bin/env python3
"""
OKX API 全面测试脚本

测试所有修改后的OKX API调用，确保参数格式正确
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_api_calls():
    """测试OKX API调用"""
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 错误: 请确保在.env文件中设置了OKX API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,  # 设置为True使用测试环境
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                # OKX不需要warnOnFetchOpenOrdersWithoutSymbol参数
                'recvWindow': 10000
            }
        })
        
        print("✅ OKX交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC/USDT:USDT'
        
        print(f"📊 测试交易对: {symbol}")
        
        # 1. 测试获取市场数据
        try:
            print("\n🔄 测试获取市场数据...")
            ticker = exchange.fetch_ticker(symbol)
            print(f"✅ 获取ticker成功: 价格 ${ticker['last']}")
            
            ohlcv = exchange.fetch_ohlcv(symbol, '15m', limit=10)
            print(f"✅ 获取K线数据成功: {len(ohlcv)} 条记录")
        except Exception as e:
            print(f"❌ 获取市场数据失败: {str(e)}")
        
        # 2. 测试获取账户余额
        try:
            print("\n🔄 测试获取账户余额...")
            balance = exchange.fetch_balance()
            print(f"✅ 获取余额成功: {len(balance['total'])} 种资产")
        except Exception as e:
            print(f"❌ 获取余额失败: {str(e)}")
        
        # 3. 测试获取持仓信息
        try:
            print("\n🔄 测试获取持仓信息...")
            positions = exchange.fetch_positions([symbol])
            print(f"✅ 获取持仓成功: {len(positions)} 个持仓")
            
            for pos in positions:
                if pos['contracts'] != 0 or pos['side'] is not None:
                    print(f"📊 持仓: {pos['side']} - 数量: {pos['contracts']} - 杠杆: {pos.get('leverage', 'N/A')}x")
        except Exception as e:
            print(f"❌ 获取持仓失败: {str(e)}")
        
        # 4. 测试获取未完成订单
        try:
            print("\n🔄 测试获取未完成订单...")
            orders = exchange.fetch_open_orders(symbol=symbol)
            print(f"✅ 获取订单成功: {len(orders)} 个未完成订单")
        except Exception as e:
            print(f"❌ 获取订单失败: {str(e)}")
        
        # 5. 测试杠杆设置（已在之前的脚本中测试过）
        print("\n🔄 测试杠杆设置...")
        try:
            # 测试多头杠杆
            result = exchange.set_leverage(2, symbol, params={
                'marginMode': 'isolated',
                'posSide': 'long',
                'type': 'swap'
            })
            print(f"✅ 多头杠杆设置成功")
            
            # 测试空头杠杆
            result = exchange.set_leverage(2, symbol, params={
                'marginMode': 'isolated',
                'posSide': 'short',
                'type': 'swap'
            })
            print(f"✅ 空头杠杆设置成功")
        except Exception as e:
            print(f"❌ 杠杆设置失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接OKX失败: {str(e)}")
        return False

def test_parameter_formats():
    """测试参数格式"""
    print("\n📋 参数格式测试:")
    
    # 测试交易对格式
    symbols = [
        'BTC/USDT:USDT',
        'ETH/USDT:USDT', 
        'BNB/USDT:USDT'
    ]
    
    for symbol in symbols:
        base_quote = symbol.split('/')
        if len(base_quote) >= 2:
            base = base_quote[0]
            quote_part = base_quote[1]
            if ':' in quote_part:
                quote = quote_part.split(':')[0]
                print(f"✅ {symbol}: base={base}, quote={quote}")
            else:
                print(f"❌ {symbol}: 格式错误")
    
    # 测试参数格式
    test_params = {
        'marginMode': 'isolated',
        'posSide': 'long',  # OKX格式
        'type': 'swap'
    }
    print(f"✅ OKX参数格式: {test_params}")
    
    # 对比币安格式（错误的）
    binance_params = {
        'marginMode': 'isolated',
        'positionSide': 'BOTH',  # 币安格式（错误）
        'type': 'future'
    }
    print(f"❌ 币安参数格式（错误）: {binance_params}")

if __name__ == "__main__":
    print("🚀 OKX API 全面测试开始")
    print("=" * 60)
    
    # 参数格式测试
    test_parameter_formats()
    
    print("\n" + "=" * 60)
    
    # API调用测试
    success = test_okx_api_calls()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有测试完成，OKX API集成正常")
    else:
        print("❌ 测试失败，请检查API配置")
