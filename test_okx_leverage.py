#!/usr/bin/env python3
"""
OKX杠杆设置测试脚本

用于测试OKX API的杠杆设置功能，确保参数格式正确
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_leverage():
    """测试OKX杠杆设置"""
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 错误: 请确保在.env文件中设置了OKX API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,  # 设置为True使用测试环境
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
            }
        })
        
        print("✅ OKX交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC/USDT:USDT'
        leverage = 2
        
        print(f"📊 测试交易对: {symbol}")
        print(f"🔧 测试杠杆倍数: {leverage}x")
        
        # 方法1: 设置多头杠杆
        try:
            print("\n🔄 正在设置多头杠杆...")
            result1 = exchange.set_leverage(leverage, symbol, params={
                'marginMode': 'isolated',
                'posSide': 'long',
                'type': 'swap'
            })
            print(f"✅ 多头杠杆设置成功: {result1}")
        except Exception as e:
            print(f"❌ 多头杠杆设置失败: {str(e)}")
        
        # 方法2: 设置空头杠杆
        try:
            print("\n🔄 正在设置空头杠杆...")
            result2 = exchange.set_leverage(leverage, symbol, params={
                'marginMode': 'isolated',
                'posSide': 'short',
                'type': 'swap'
            })
            print(f"✅ 空头杠杆设置成功: {result2}")
        except Exception as e:
            print(f"❌ 空头杠杆设置失败: {str(e)}")
        
        # 方法3: 尝试使用net模式（单向持仓）
        try:
            print("\n🔄 正在测试net模式杠杆...")
            result3 = exchange.set_leverage(leverage, symbol, params={
                'marginMode': 'isolated',
                'posSide': 'net',
                'type': 'swap'
            })
            print(f"✅ net模式杠杆设置成功: {result3}")
        except Exception as e:
            print(f"❌ net模式杠杆设置失败: {str(e)}")
        
        # 获取当前杠杆设置
        try:
            print("\n📋 正在获取当前杠杆设置...")
            positions = exchange.fetch_positions([symbol])
            for pos in positions:
                if pos['contracts'] != 0 or pos['side'] is not None:
                    print(f"📊 持仓信息: {pos['side']} - 杠杆: {pos.get('leverage', 'N/A')}x")
        except Exception as e:
            print(f"❌ 获取杠杆信息失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接OKX失败: {str(e)}")
        return False

def test_different_symbols():
    """测试不同交易对的杠杆设置"""
    
    symbols = [
        'BTC/USDT:USDT',
        'ETH/USDT:USDT',
        'BNB/USDT:USDT'
    ]
    
    load_dotenv()
    
    try:
        exchange = ccxt.okx({
            'apiKey': os.getenv('OKX_API_KEY'),
            'secret': os.getenv('OKX_SECRET_KEY'),
            'password': os.getenv('OKX_PASSPHRASE'),
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',
            }
        })
        
        for symbol in symbols:
            print(f"\n🔧 测试交易对: {symbol}")
            try:
                # 测试设置2倍杠杆
                exchange.set_leverage(2, symbol, params={
                    'marginMode': 'isolated',
                    'posSide': 'long',
                    'type': 'swap'
                })
                print(f"✅ {symbol} 杠杆设置成功")
            except Exception as e:
                print(f"❌ {symbol} 杠杆设置失败: {str(e)}")
                
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 OKX杠杆设置测试开始")
    print("=" * 50)
    
    # 基本杠杆设置测试
    success = test_okx_leverage()
    
    if success:
        print("\n" + "=" * 50)
        print("🔧 测试不同交易对...")
        test_different_symbols()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")
