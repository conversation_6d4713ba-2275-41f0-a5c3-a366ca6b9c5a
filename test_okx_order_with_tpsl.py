#!/usr/bin/env python3
"""
OKX下单附带止盈止损测试脚本

测试OKX API在下单时直接附带止盈止损参数的功能
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_order_with_tpsl():
    """测试OKX下单附带止盈止损"""
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 错误: 请确保在.env文件中设置了OKX API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': True,  # 使用测试环境，避免真实交易
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
            }
        })
        
        print("✅ OKX交易所连接成功（测试环境）")
        
        # 测试参数
        symbol = 'BTC/USDT:USDT'
        side = 'buy'
        amount = 0.001  # 最小交易量
        
        # 获取当前价格
        ticker = exchange.fetch_ticker(symbol)
        current_price = ticker['last']
        
        print(f"📊 当前价格: ${current_price}")
        
        # 计算止盈止损价格
        tp_price = current_price * 1.02  # 2%止盈
        sl_price = current_price * 0.98  # 2%止损
        
        print(f"🎯 止盈价格: ${tp_price:.2f}")
        print(f"🛡️ 止损价格: ${sl_price:.2f}")
        
        # 方法1: 测试市价单附带止盈止损
        print("\n🔄 测试方法1: 市价单附带止盈止损")
        try:
            order_params = {
                'marginMode': 'isolated',
                'posSide': 'long',
                'tpTriggerPx': str(tp_price),  # 止盈触发价
                'tpOrdPx': '-1',  # 止盈委托价，-1表示市价
                'slTriggerPx': str(sl_price),  # 止损触发价  
                'slOrdPx': '-1',  # 止损委托价，-1表示市价
                'tpTriggerPxType': 'last',  # 止盈触发价类型
                'slTriggerPxType': 'last'   # 止损触发价类型
            }
            
            print(f"📋 订单参数: {order_params}")
            
            # 注意：这里只是测试参数格式，不会真实下单
            print("✅ 方法1参数格式正确")
            
        except Exception as e:
            print(f"❌ 方法1失败: {str(e)}")
        
        # 方法2: 测试限价单附带止盈止损
        print("\n🔄 测试方法2: 限价单附带止盈止损")
        try:
            limit_price = current_price * 0.999  # 稍低于市价的限价
            
            order_params = {
                'marginMode': 'isolated',
                'posSide': 'long',
                'tpTriggerPx': str(tp_price),
                'tpOrdPx': '-1',
                'slTriggerPx': str(sl_price),
                'slOrdPx': '-1',
                'tpTriggerPxType': 'last',
                'slTriggerPxType': 'last'
            }
            
            print(f"📋 限价: ${limit_price:.2f}")
            print(f"📋 订单参数: {order_params}")
            
            print("✅ 方法2参数格式正确")
            
        except Exception as e:
            print(f"❌ 方法2失败: {str(e)}")
        
        # 方法3: 测试不同的触发价类型
        print("\n🔄 测试方法3: 不同触发价类型")
        try:
            # 测试使用标记价格作为触发价类型
            order_params = {
                'marginMode': 'isolated',
                'posSide': 'long',
                'tpTriggerPx': str(tp_price),
                'tpOrdPx': '-1',
                'slTriggerPx': str(sl_price),
                'slOrdPx': '-1',
                'tpTriggerPxType': 'mark',  # 使用标记价格
                'slTriggerPxType': 'mark'   # 使用标记价格
            }
            
            print(f"📋 使用标记价格触发: {order_params}")
            print("✅ 方法3参数格式正确")
            
        except Exception as e:
            print(f"❌ 方法3失败: {str(e)}")
        
        # 方法4: 测试空头订单的止盈止损
        print("\n🔄 测试方法4: 空头订单止盈止损")
        try:
            # 空头的止盈止损价格相反
            short_tp_price = current_price * 0.98  # 空头止盈价格更低
            short_sl_price = current_price * 1.02  # 空头止损价格更高
            
            order_params = {
                'marginMode': 'isolated',
                'posSide': 'short',
                'tpTriggerPx': str(short_tp_price),
                'tpOrdPx': '-1',
                'slTriggerPx': str(short_sl_price),
                'slOrdPx': '-1',
                'tpTriggerPxType': 'last',
                'slTriggerPxType': 'last'
            }
            
            print(f"📋 空头止盈: ${short_tp_price:.2f}")
            print(f"📋 空头止损: ${short_sl_price:.2f}")
            print(f"📋 订单参数: {order_params}")
            print("✅ 方法4参数格式正确")
            
        except Exception as e:
            print(f"❌ 方法4失败: {str(e)}")
        
        # 获取账户信息验证权限
        print("\n🔄 验证账户权限...")
        try:
            balance = exchange.fetch_balance()
            print(f"✅ 账户验证成功，资产种类: {len(balance['total'])}")
        except Exception as e:
            print(f"❌ 账户验证失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接OKX失败: {str(e)}")
        return False

def show_okx_tpsl_documentation():
    """显示OKX止盈止损参数文档"""
    print("\n📚 OKX止盈止损参数说明:")
    print("=" * 60)
    print("tpTriggerPx: 止盈触发价")
    print("tpOrdPx: 止盈委托价 (-1表示市价)")
    print("slTriggerPx: 止损触发价")
    print("slOrdPx: 止损委托价 (-1表示市价)")
    print("tpTriggerPxType: 止盈触发价类型")
    print("  - last: 最新价")
    print("  - index: 指数价格")
    print("  - mark: 标记价格")
    print("slTriggerPxType: 止损触发价类型")
    print("  - last: 最新价")
    print("  - index: 指数价格") 
    print("  - mark: 标记价格")
    print("=" * 60)

if __name__ == "__main__":
    print("🚀 OKX下单附带止盈止损测试开始")
    print("=" * 60)
    
    # 显示参数文档
    show_okx_tpsl_documentation()
    
    # 运行测试
    success = test_okx_order_with_tpsl()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有测试完成，OKX下单附带止盈止损参数格式正确")
        print("💡 提示: 实际下单时请确保在真实环境中测试小额订单")
    else:
        print("❌ 测试失败，请检查API配置")
